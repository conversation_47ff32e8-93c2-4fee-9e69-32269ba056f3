"use client";

import Image from "next/image";
import Link from "next/link";
import { useState, useRef, useEffect } from "react";

// Generate a random user ID
const generateUserId = () => {
  return Math.random().toString(36).substring(2, 10).toUpperCase();
};

export default function Home() {
  const [screenShareActive, setScreenShareActive] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<string | null>(null);
  const [userId, setUserId] = useState<string>("");
  const [autoUpload, setAutoUpload] = useState(false);
  const [imageQuality, setImageQuality] = useState(0.8); // JPEG quality (0.1 - 1.0)
  const [imageFormat, setImageFormat] = useState<'jpeg' | 'png' | 'webp'>('jpeg');
  const [maxWidth, setMaxWidth] = useState(1920); // Max width for downscaling
  const [maxHeight, setMaxHeight] = useState(1080); // Max height for downscaling
  const [lastFileSize, setLastFileSize] = useState<number | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const uploadIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Generate user ID on component mount
  useEffect(() => {
    setUserId(generateUserId());
  }, []);

  // Helper function to estimate file size based on settings
  const getEstimatedSizeInfo = () => {
    const baseSize = maxWidth * maxHeight * 3; // Rough estimate for RGB data
    let compressionFactor = 1;

    switch (imageFormat) {
      case 'jpeg':
        compressionFactor = imageQuality * 0.1; // JPEG compression
        break;
      case 'webp':
        compressionFactor = imageQuality * 0.08; // WebP is more efficient
        break;
      case 'png':
        compressionFactor = 0.3; // PNG compression
        break;
    }

    const estimatedBytes = baseSize * compressionFactor;
    const estimatedKB = Math.round(estimatedBytes / 1024);

    return {
      estimatedKB,
      compressionRatio: Math.round((1 - compressionFactor) * 100)
    };
  };

  // Auto upload timer
  useEffect(() => {
    if (autoUpload && screenShareActive) {
      const uploadTimer = setInterval(() => {
        handleScreenshotUpload();
      }, 1000); // Upload every 1 second

      uploadIntervalRef.current = uploadTimer;

      return () => {
        clearInterval(uploadTimer);
      };
    } else {
      // Clear timer when auto upload is disabled
      if (uploadIntervalRef.current) {
        clearInterval(uploadIntervalRef.current);
        uploadIntervalRef.current = null;
      }
    }
  }, [autoUpload, screenShareActive]);

  const startScreenShare = async () => {
    try {
      const stream = await navigator.mediaDevices.getDisplayMedia({ video: true });
      if (!stream) {
        console.error("No stream received from getDisplayMedia.");
        return;
      }

      if (!videoRef.current) {
        console.error("Video element not available.");
        return;
      }

      videoRef.current.srcObject = stream;
      videoRef.current.onloadedmetadata = () => {
        videoRef.current?.play();
      };
      setScreenShareActive(true);
      setAutoUpload(true);
    } catch (error) {
      console.error("Error starting screen share:", error);
    }
  };

  const stopScreenShare = () => {
    if (videoRef.current?.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setScreenShareActive(false);
    setAutoUpload(false);
    setUploadResult(null);
  };

  const captureScreenshot = async () => {
    if (!videoRef.current || !canvasRef.current) {
      console.error("Video or canvas element not available.");
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      console.error("Canvas context not available.");
      return;
    }

    // Calculate optimal dimensions with aspect ratio preservation
    const originalWidth = video.videoWidth;
    const originalHeight = video.videoHeight;
    const aspectRatio = originalWidth / originalHeight;

    let targetWidth = originalWidth;
    let targetHeight = originalHeight;

    // Apply max width/height constraints while preserving aspect ratio
    if (targetWidth > maxWidth) {
      targetWidth = maxWidth;
      targetHeight = targetWidth / aspectRatio;
    }
    if (targetHeight > maxHeight) {
      targetHeight = maxHeight;
      targetWidth = targetHeight * aspectRatio;
    }

    // Set canvas dimensions to optimized size
    canvas.width = Math.round(targetWidth);
    canvas.height = Math.round(targetHeight);

    // Enable image smoothing for better quality when downscaling
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // Draw the current video frame to canvas with scaling
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob with specified format and quality
    const mimeType = imageFormat === 'jpeg' ? 'image/jpeg' :
      imageFormat === 'webp' ? 'image/webp' : 'image/png';

    return new Promise<Blob | null>((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob);
      }, mimeType, imageFormat === 'png' ? undefined : imageQuality);
    });
  };

  const handleScreenshotUpload = async () => {
    setIsUploading(true);
    setUploadResult(null);

    try {
      const blob = await captureScreenshot();
      if (!blob) {
        throw new Error('Failed to capture screenshot');
      }

      // Create form data with appropriate filename extension
      const formData = new FormData();
      const extension = imageFormat === 'jpeg' ? 'jpg' : imageFormat;
      formData.append('screenshot', blob, `screenshot.${extension}`);
      formData.append('userId', userId);

      // Upload using API route
      const response = await fetch('/api/upload-screenshot', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        setLastFileSize(blob.size);
        const sizeKB = Math.round(blob.size / 1024);
        setUploadResult(`Screenshot saved! Size: ${sizeKB}KB - URL: ${result.url}`);
      } else {
        setUploadResult(`Upload failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error uploading screenshot:', error);
      setUploadResult('Failed to upload screenshot');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <Image
          className="dark:invert"
          src="/next.svg"
          alt="Next.js logo"
          width={180}
          height={38}
          priority
        />
        <ol className="list-inside list-decimal text-sm/6 text-center sm:text-left font-[family-name:var(--font-geist-mono)]">
          <li className="mb-2 tracking-[-.01em]">
            Get started by editing{" "}
            <code className="bg-black/[.05] dark:bg-white/[.06] px-1 py-0.5 rounded font-[family-name:var(--font-geist-mono)] font-semibold">
              src/app/page.tsx
            </code>
            .
          </li>
          <li className="tracking-[-.01em]">
            Save and see your changes instantly.
          </li>
        </ol>

        <div className="flex gap-4 items-center flex-col sm:flex-row">
          <a
            className="rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:w-auto"
            href="https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Image
              className="dark:invert"
              src="/vercel.svg"
              alt="Vercel logomark"
              width={20}
              height={20}
            />
            Deploy now
          </a>
          <a
            className="rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent font-medium text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 w-full sm:w-auto md:w-[158px]"
            href="https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            Read our docs
          </a>
        </div>

        <div className="flex flex-col items-center justify-center mt-8">
          <h1 className="text-2xl font-bold mb-4">Screen Share</h1>

          {/* User ID Display */}
          <div className="mb-4 p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">Your Session ID:</p>
            <p className="text-lg font-mono font-bold text-blue-600 dark:text-blue-400">
              {userId || "Loading..."}
            </p>
            {userId && (
              <Link
                href={`/${userId}`}
                className="inline-block mt-2 text-sm text-blue-600 dark:text-blue-400 hover:underline"
              >
                View your screenshot →
              </Link>
            )}
          </div>

          {/* Image Optimization Settings */}
          <div className="mb-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
              Image Optimization Settings
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Format Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Image Format
                </label>
                <select
                  value={imageFormat}
                  onChange={(e) => setImageFormat(e.target.value as 'jpeg' | 'png' | 'webp')}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="jpeg">JPEG (Smaller, lossy)</option>
                  <option value="webp">WebP (Best compression)</option>
                  <option value="png">PNG (Larger, lossless)</option>
                </select>
              </div>

              {/* Quality Setting (only for JPEG/WebP) */}
              {imageFormat !== 'png' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Quality: {Math.round(imageQuality * 100)}%
                  </label>
                  <input
                    type="range"
                    min="0.1"
                    max="1"
                    step="0.1"
                    value={imageQuality}
                    onChange={(e) => setImageQuality(parseFloat(e.target.value))}
                    className="w-full"
                  />
                </div>
              )}

              {/* Max Width */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Width (px)
                </label>
                <select
                  value={maxWidth}
                  onChange={(e) => setMaxWidth(parseInt(e.target.value))}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="1920">1920px (Full HD)</option>
                  <option value="1280">1280px (HD)</option>
                  <option value="854">854px (480p)</option>
                  <option value="640">640px (360p)</option>
                </select>
              </div>

              {/* Max Height */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Height (px)
                </label>
                <select
                  value={maxHeight}
                  onChange={(e) => setMaxHeight(parseInt(e.target.value))}
                  className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  <option value="1080">1080px (Full HD)</option>
                  <option value="720">720px (HD)</option>
                  <option value="480">480px (480p)</option>
                  <option value="360">360px (360p)</option>
                </select>
              </div>
            </div>

            <div className="mt-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
              <div className="text-sm text-blue-800 dark:text-blue-200">
                📊 Estimated file size: ~{getEstimatedSizeInfo().estimatedKB}KB
                {lastFileSize && (
                  <span className="ml-2 text-green-600 dark:text-green-400">
                    (Last: {Math.round(lastFileSize / 1024)}KB)
                  </span>
                )}
              </div>
            </div>

            <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
              💡 Tip: Use JPEG or WebP with lower quality and resolution for smaller file sizes.
              WebP offers the best compression but may not be supported in all browsers.
            </div>
          </div>

          {!screenShareActive ? (
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={startScreenShare}
            >
              Start Live Screen Share
            </button>
          ) : (
            <button
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
              onClick={stopScreenShare}
            >
              Stop Screen Share
            </button>
          )}
          <video
            ref={videoRef}
            className={`border rounded mt-4 ${screenShareActive ? "block" : "hidden"}`}
            width="640"
            height="360"
            controls
          ></video>

          {screenShareActive && (
            <div className="mt-4 flex flex-col items-center gap-2">
              <div className="flex items-center gap-2 p-3 bg-green-100 dark:bg-green-900 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-800 dark:text-green-200 font-medium">
                  Live streaming - uploading every second
                </span>
              </div>

              {uploadResult && (
                <div className={`text-sm p-2 rounded max-w-md text-center ${uploadResult.includes('successfully')
                  ? "bg-blue-100 text-blue-800"
                  : "bg-red-100 text-red-800"
                  }`}>
                  {uploadResult}
                </div>
              )}
            </div>
          )}

          {/* Hidden canvas for screenshot capture */}
          <canvas
            ref={canvasRef}
            className="hidden"
          ></canvas>
        </div>
      </main>
      <footer className="row-start-3 flex gap-[24px] flex-wrap items-center justify-center">
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/file.svg"
            alt="File icon"
            width={16}
            height={16}
          />
          Learn
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/window.svg"
            alt="Window icon"
            width={16}
            height={16}
          />
          Examples
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/globe.svg"
            alt="Globe icon"
            width={16}
            height={16}
          />
          Go to nextjs.org →
        </a>
      </footer>
    </div>
  );
}
